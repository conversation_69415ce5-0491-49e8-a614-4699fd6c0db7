# Configuración de la aplicación
APP_NAME="CRM Asesoría Universitaria"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000

# Configuración de base de datos
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=omm_crm
DB_USERNAME=root
DB_PASSWORD=

# Configuración de logging
LOG_LEVEL=debug
LOG_CHANNEL=single

# Configuración de email (para envío de presupuestos)
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Configuración de archivos
UPLOAD_PATH=storage/uploads
PDF_STORAGE_PATH=storage/pdfs
MAX_FILE_SIZE=10485760

# Configuración de seguridad
JWT_SECRET=your-secret-key-here
SESSION_LIFETIME=120

# Configuración de desarrollo
CORS_ALLOWED_ORIGINS=http://localhost:3000
API_PREFIX=/api/v1
