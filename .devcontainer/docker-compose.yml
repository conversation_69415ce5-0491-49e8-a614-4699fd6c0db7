version: '3.8'

services: 
  app:
    build:
      context: .
      dockerfile: Dockerfile

    volumes:
      - ../..:/workspaces:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally. 
    # (Adding the "ports" property to this file will not forward from a Codespace.)

  db:
    image: mariadb:10.4
    restart: unless-stopped
    volumes:
      - mariadb-data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: mariadb
      MYSQL_DATABASE: mariadb
      MYSQL_USER: mariadb
      MYSQL_PASSWORD: mariadb

    # Add "forwardPorts": ["3306"] to **devcontainer.json** to forward MariaDB locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)
  
volumes:
  mariadb-data:
