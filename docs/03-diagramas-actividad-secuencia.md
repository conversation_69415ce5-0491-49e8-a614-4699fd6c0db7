# Diagramas de Actividad y Secuencia - CRM Personalizado

## 1. Diagrama de Actividad - Proceso Completo de Cliente

```mermaid
flowchart TD
    A[Cliente contacta] --> B[Registrar cliente]
    B --> C[Crear expediente]
    C --> D[Registrar comunicación]
    D --> E{¿Servicio académico?}
    
    E -->|Sí| F[Marcar checkbox académico]
    F --> G[Crear servicio académico]
    G --> H[Convertir a contratado]
    H --> I[Trabajar en servicio]
    I --> J[Generar factura]
    J --> K[Procesar pago final]
    K --> L[Fin]
    
    E -->|No| M[Crear presupuesto]
    M --> N[Enviar presupuesto]
    N --> O{¿Cliente acepta?}
    
    O -->|No| P[Marcar rechazado]
    P --> Q[Fin - No contratado]
    
    O -->|Sí| R[Marcar aceptado]
    R --> S[Convertir a contratado]
    S --> T[Crear gestión servicio]
    T --> U[Registrar pago anticipo]
    U --> V{¿Inicio inmediato?}
    
    V -->|No| W[Registrar demora]
    W --> X[Esperar fecha inicio]
    X --> Y[Iniciar servicio]
    
    V -->|Sí| Y
    Y --> Z[Trabajar en servicio]
    Z --> AA{¿Pagos intermedios?}
    
    AA -->|Sí| BB[Registrar pagos]
    BB --> Z
    
    AA -->|No| CC[Finalizar servicio]
    CC --> DD[Generar factura]
    DD --> EE[Procesar pago final]
    EE --> L
```

## 2. Diagrama de Actividad - Gestión de Pagos

```mermaid
flowchart TD
    A[Iniciar registro pago] --> B{¿Tipo de pago?}
    
    B -->|Único| C[Registrar pago único]
    C --> D[Definir cantidad]
    D --> E[Seleccionar forma pago]
    E --> F[Guardar pago]
    
    B -->|Fraccionado| G[Crear pago fraccionado]
    G --> H[Definir número plazos]
    H --> I[Para cada plazo]
    I --> J[Definir cantidad plazo]
    J --> K[Definir fecha plazo]
    K --> L{¿Más plazos?}
    
    L -->|Sí| I
    L -->|No| M[Guardar pago y plazos]
    
    F --> N[Actualizar totales servicio]
    M --> N
    N --> O{¿Pago realizado?}
    
    O -->|Sí| P[Marcar como pagado]
    P --> Q[Registrar fecha pago]
    Q --> R{¿Es fraccionado?}
    
    R -->|Sí| S{¿Todos plazos pagados?}
    S -->|Sí| T[Marcar pago completo]
    S -->|No| U[Mantener pendiente]
    
    R -->|No| T
    T --> V[Recalcular totales]
    U --> V
    V --> W[Fin]
    
    O -->|No| X[Mantener pendiente]
    X --> W
```

## 3. Diagrama de Secuencia - Registro de Cliente y Presupuesto

```mermaid
sequenceDiagram
    participant C as Cliente
    participant A as Admin
    participant UI as Interfaz
    participant CC as ClienteController
    participant CS as ClienteService
    participant ES as ExpedienteService
    participant ComS as ComunicacionService
    participant DB as Database
    participant Email as EmailService
    
    C->>A: Contacto inicial
    A->>UI: Abrir formulario cliente
    UI->>A: Mostrar formulario
    A->>UI: Completar datos cliente
    UI->>CC: POST /clientes
    CC->>CS: crearCliente(datos)
    CS->>DB: INSERT cliente
    DB-->>CS: cliente_id
    
    CS->>ES: crearExpediente(cliente_id)
    ES->>DB: INSERT expediente
    DB-->>ES: expediente_id
    
    CS->>ComS: crearComunicacion(cliente_id, expediente_id)
    ComS->>DB: INSERT comunicacion
    DB-->>ComS: comunicacion_id
    
    ComS-->>CS: comunicacion_id
    ES-->>CS: expediente_id
    CS-->>CC: cliente creado
    CC-->>UI: 201 Created
    UI-->>A: Cliente registrado exitosamente
    
    A->>UI: Crear presupuesto
    UI->>CC: POST /presupuestos
    CC->>ES: crearPresupuesto(expediente_id, datos)
    ES->>DB: INSERT presupuesto
    DB-->>ES: presupuesto_id
    
    A->>UI: Enviar presupuesto
    UI->>CC: POST /presupuestos/{id}/enviar
    CC->>Email: enviarPresupuesto(cliente_email, pdf)
    Email-->>CC: enviado
    CC->>ES: actualizarEstado(presupuesto_id, "enviado")
    ES->>DB: UPDATE presupuesto
    DB-->>ES: actualizado
    ES-->>CC: estado actualizado
    CC-->>UI: 200 OK
    UI-->>A: Presupuesto enviado
```

## 4. Diagrama de Secuencia - Conversión a Cliente Contratado

```mermaid
sequenceDiagram
    participant A as Admin
    participant UI as Interfaz
    participant PC as PresupuestoController
    participant CS as ClienteService
    participant SS as ServicioService
    participant PS as PagoService
    participant DB as Database
    
    A->>UI: Cliente acepta presupuesto
    UI->>PC: PUT /presupuestos/{id}/aceptar
    PC->>CS: convertirAContratado(cliente_id)
    
    CS->>DB: UPDATE cliente SET tipo='contratado'
    DB-->>CS: cliente actualizado
    
    CS->>SS: crearServicioDesdePresupuesto(presupuesto_id)
    SS->>DB: INSERT servicio
    DB-->>SS: servicio_id
    
    SS->>PS: inicializarPagos(servicio_id)
    PS->>DB: Preparar estructura pagos
    DB-->>PS: estructura creada
    
    PS-->>SS: pagos inicializados
    SS-->>CS: servicio creado
    CS-->>PC: conversión completada
    PC-->>UI: 200 OK
    UI-->>A: Cliente convertido exitosamente
    
    A->>UI: Registrar pago anticipo
    UI->>PS: POST /pagos
    PS->>DB: INSERT pago
    DB-->>PS: pago_id
    PS->>SS: actualizarTotales(servicio_id)
    SS->>DB: UPDATE servicio totales
    DB-->>SS: totales actualizados
    SS-->>PS: servicio actualizado
    PS-->>UI: 201 Created
    UI-->>A: Pago registrado
```

## 5. Diagrama de Secuencia - Procesamiento de Factura con IA

```mermaid
sequenceDiagram
    participant A as Admin
    participant UI as Interfaz
    participant FC as FacturaController
    participant FS as FacturaService
    participant IAS as IAService
    participant FileS as FileService
    participant DB as Database
    
    A->>UI: Subir PDF factura
    UI->>FC: POST /facturas/upload
    FC->>FileS: guardarPDF(archivo)
    FileS-->>FC: ruta_archivo
    
    FC->>FS: crearFactura(cliente_id, ruta_archivo)
    FS->>DB: INSERT factura (datos básicos)
    DB-->>FS: factura_id
    
    FS->>IAS: procesarPDF(ruta_archivo)
    IAS-->>FS: datos_extraidos
    
    FS->>DB: UPDATE factura SET datos_extraidos
    DB-->>FS: factura actualizada
    
    FS-->>FC: factura_id, datos_extraidos
    FC-->>UI: 201 Created + datos para validación
    UI-->>A: Mostrar datos extraídos para validación
    
    A->>UI: Validar y corregir datos
    UI->>FC: PUT /facturas/{id}/validar
    FC->>FS: validarDatos(factura_id, datos_validados)
    FS->>DB: UPDATE factura SET datos_validados
    DB-->>FS: factura validada
    
    A->>UI: Vincular servicios
    UI->>FC: POST /facturas/{id}/servicios
    FC->>FS: vincularServicios(factura_id, servicios_ids)
    FS->>DB: INSERT factura_servicios
    DB-->>FS: vinculaciones creadas
    
    FS-->>FC: factura completada
    FC-->>UI: 200 OK
    UI-->>A: Factura procesada exitosamente
```

## 6. Diagrama de Actividad - Gestión de Servicios Académicos

```mermaid
flowchart TD
    A[Cliente solicita servicio menor] --> B[Admin registra comunicación]
    B --> C{¿Es servicio académico?}
    
    C -->|No| D[Comunicación normal]
    D --> E[Fin]
    
    C -->|Sí| F[Marcar checkbox académico]
    F --> G{¿Cliente es contratado?}
    
    G -->|Sí| H[Crear servicio académico]
    G -->|No| I[Convertir a contratado]
    I --> H
    
    H --> J[Vincular con comunicación]
    J --> K[Trabajar en servicio]
    K --> L{¿Más servicios académicos?}
    
    L -->|Sí| M[Registrar nueva comunicación]
    M --> F
    
    L -->|No| N[Generar factura conjunto]
    N --> O[Procesar pago]
    O --> P[Fin]
```

## 7. Diagrama de Secuencia - Gestión de Servicios Académicos

```mermaid
sequenceDiagram
    participant A as Admin
    participant UI as Interfaz
    participant ComC as ComunicacionController
    participant ComS as ComunicacionService
    participant CS as ClienteService
    participant SAS as ServicioAcademicoService
    participant DB as Database
    
    A->>UI: Registrar comunicación
    UI->>ComC: POST /comunicaciones
    ComC->>ComS: crearComunicacion(datos)
    
    A->>UI: Marcar checkbox académico
    UI->>ComC: PUT /comunicaciones/{id}/academico
    ComC->>ComS: marcarComoAcademico(comunicacion_id)
    
    ComS->>CS: verificarTipoCliente(cliente_id)
    CS->>DB: SELECT tipo FROM clientes
    DB-->>CS: tipo_cliente
    
    alt Cliente no es contratado
        CS->>DB: UPDATE cliente SET tipo='contratado'
        DB-->>CS: cliente actualizado
    end
    
    ComS->>SAS: crearServicioAcademico(comunicacion_id, cliente_id)
    SAS->>DB: INSERT servicio_academico
    DB-->>SAS: servicio_id
    
    SAS->>ComS: vincularServicio(comunicacion_id, servicio_id)
    ComS->>DB: UPDATE comunicacion SET servicio_id
    DB-->>ComS: vinculación creada
    
    ComS-->>ComC: servicio académico creado
    ComC-->>UI: 200 OK
    UI-->>A: Servicio académico registrado
```

## 8. Estados y Transiciones

### 8.1 Estados del Cliente
```mermaid
stateDiagram-v2
    [*] --> Potencial: Registro inicial
    Potencial --> Contratado: Acepta presupuesto
    Potencial --> Contratado: Solicita servicio académico
    Contratado --> Potencial: Reversión manual
    Contratado --> [*]: Finalización relación
```

### 8.2 Estados del Presupuesto
```mermaid
stateDiagram-v2
    [*] --> Generado: Creación
    Generado --> Enviado: Envío por email
    Enviado --> Aceptado: Cliente acepta
    Enviado --> Rechazado: Cliente rechaza
    Aceptado --> [*]: Conversión a servicio
    Rechazado --> [*]: Fin proceso
```

### 8.3 Estados del Pago
```mermaid
stateDiagram-v2
    [*] --> Pendiente: Creación
    Pendiente --> Ordenado: Cliente inicia pago
    Ordenado --> Pagado: Confirmación pago
    Pendiente --> Pagado: Pago directo
    Pagado --> [*]: Pago completado
```

### 8.4 Estados del Servicio
```mermaid
stateDiagram-v2
    [*] --> Creado: Desde presupuesto/comunicación
    Creado --> Iniciado: Fecha inicio
    Iniciado --> EnProceso: Trabajo en curso
    EnProceso --> Completado: Finalización
    Completado --> Facturado: Generación factura
    Facturado --> [*]: Pago final
```
