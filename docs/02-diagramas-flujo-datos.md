# Diagramas de Flujo de Datos - CRM Personalizado

## 1. Diagrama de Contexto (Nivel 0)

```mermaid
graph TB
    Cliente[Cliente] --> |Solicita servicios| CRM[Sistema CRM]
    CRM --> |Presupuestos/Facturas| Cliente
    
    Admin[Usuario Administrativo] --> |Gestiona datos| CRM
    CRM --> |Reportes/Información| Admin
    
    EmailSys[Sistema Email] --> |Confirmaciones envío| CRM
    CRM --> |Presupuestos| EmailSys
    
    IASys[Sistema IA] --> |Datos extraídos| CRM
    CRM --> |PDFs para procesar| IASys
    
    DB[(Base de Datos)] --> |Datos almacenados| CRM
    CRM --> |Operaciones CRUD| DB
```

## 2. Diagrama de Nivel 1 - Procesos Principales

```mermaid
graph TB
    subgraph "Sistema CRM"
        P1[1.0 Gestión de Clientes]
        P2[2.0 Gestión de Comunicaciones]
        P3[3.0 Gestión de Expedientes]
        P4[4.0 Gestión de Servicios]
        P5[5.0 Gestión de Pagos]
        P6[6.0 Gestión de Facturas]
    end
    
    Cliente[Cliente] --> |Datos personales| P1
    Cliente --> |Comunicaciones| P2
    
    Admin[Usuario Administrativo] --> P1
    Admin --> P2
    Admin --> P3
    Admin --> P4
    Admin --> P5
    Admin --> P6
    
    P1 --> |Info cliente| P3
    P3 --> |Presupuestos| P4
    P4 --> |Servicios contratados| P5
    P4 --> |Servicios completados| P6
    
    P2 --> |Comunicaciones| P3
    P2 --> |Servicios académicos| P4
    
    EmailSys[Sistema Email] <--> P3
    IASys[Sistema IA] <--> P6
    
    DB[(Base de Datos)] <--> P1
    DB <--> P2
    DB <--> P3
    DB <--> P4
    DB <--> P5
    DB <--> P6
```

## 3. Diagrama de Nivel 2 - Gestión de Clientes (Proceso 1.0)

```mermaid
graph TB
    subgraph "1.0 Gestión de Clientes"
        P11[1.1 Registrar Cliente]
        P12[1.2 Actualizar Cliente]
        P13[1.3 Convertir a Contratado]
        P14[1.4 Consultar Cliente]
    end
    
    Admin[Usuario Administrativo] --> |Datos nuevos| P11
    Admin --> |Modificaciones| P12
    Admin --> |Consultas| P14
    
    P11 --> |Cliente registrado| D1[(Clientes)]
    P12 --> |Cliente actualizado| D1
    P14 --> |Información cliente| Admin
    
    P13 --> |Cliente contratado| D2[(Clientes Contratados)]
    
    Trigger[Aceptación Presupuesto/Servicio Académico] --> P13
    
    D1 <--> P12
    D1 <--> P13
    D1 <--> P14
    D2 <--> P14
```

## 4. Diagrama de Nivel 2 - Gestión de Servicios (Proceso 4.0)

```mermaid
graph TB
    subgraph "4.0 Gestión de Servicios"
        P41[4.1 Crear Servicio]
        P42[4.2 Gestionar Servicio Regular]
        P43[4.3 Gestionar Servicio Académico]
        P44[4.4 Actualizar Estado Servicio]
        P45[4.5 Consultar Servicios]
    end
    
    Admin[Usuario Administrativo] --> P41
    Admin --> P42
    Admin --> P43
    Admin --> P44
    Admin --> P45
    
    P41 --> |Nuevo servicio| D4[(Servicios)]
    P42 --> |Servicio actualizado| D4
    P43 --> |Servicio académico| D4
    P44 --> |Estado actualizado| D4
    P45 --> |Lista servicios| Admin
    
    D3[(Presupuestos)] --> |Presupuesto aceptado| P41
    D5[(Comunicaciones)] --> |Servicio académico| P43
    
    P42 --> |Fechas y precios| D6[(Gestión Servicios)]
    P43 --> |Texto comunicación| D7[(Servicios Académicos)]
    
    D4 <--> P45
    D6 <--> P42
    D6 <--> P44
    D7 <--> P43
```

## 5. Diagrama de Nivel 2 - Gestión de Pagos (Proceso 5.0)

```mermaid
graph TB
    subgraph "5.0 Gestión de Pagos"
        P51[5.1 Registrar Pago]
        P52[5.2 Gestionar Plazos]
        P53[5.3 Actualizar Estado Pago]
        P54[5.4 Calcular Totales]
        P55[5.5 Consultar Pagos]
    end
    
    Admin[Usuario Administrativo] --> P51
    Admin --> P52
    Admin --> P53
    Admin --> P55
    
    P51 --> |Nuevo pago| D8[(Pagos)]
    P52 --> |Plazos definidos| D9[(Plazos)]
    P53 --> |Estado actualizado| D8
    P53 --> |Estado plazo| D9
    P55 --> |Información pagos| Admin
    
    D6[(Gestión Servicios)] --> |Precio servicio| P54
    D8 --> |Pagos realizados| P54
    P54 --> |Totales calculados| D6
    
    D8 <--> P51
    D8 <--> P53
    D8 <--> P54
    D8 <--> P55
    D9 <--> P52
    D9 <--> P53
```

## 6. Diagrama de Nivel 2 - Gestión de Facturas (Proceso 6.0)

```mermaid
graph TB
    subgraph "6.0 Gestión de Facturas"
        P61[6.1 Subir PDF Factura]
        P62[6.2 Procesar con IA]
        P63[6.3 Validar Datos]
        P64[6.4 Vincular Servicios]
        P65[6.5 Almacenar Factura]
    end
    
    Admin[Usuario Administrativo] --> |PDF factura| P61
    Admin --> |Validación| P63
    Admin --> |Vinculaciones| P64
    
    P61 --> |PDF almacenado| FileStore[(Almacén PDFs)]
    P61 --> |PDF para procesar| P62
    
    P62 --> |Datos extraídos| P63
    IASys[Sistema IA] <--> P62
    
    P63 --> |Datos validados| P65
    P64 --> |Servicios vinculados| P65
    P65 --> |Factura completa| D10[(Facturas)]
    
    D4[(Servicios)] <--> P64
    FileStore --> P62
    D10 --> |Información factura| Admin
```

## 7. Flujo de Datos - Proceso Completo Cliente

```mermaid
sequenceDiagram
    participant C as Cliente
    participant A as Admin
    participant CRM as Sistema CRM
    participant DB as Base Datos
    participant Email as Sistema Email
    participant IA as Sistema IA
    
    C->>A: Contacto inicial
    A->>CRM: Registrar cliente
    CRM->>DB: Guardar cliente
    CRM->>DB: Crear expediente
    A->>CRM: Registrar comunicación
    CRM->>DB: Guardar comunicación
    
    A->>CRM: Crear presupuesto
    CRM->>DB: Guardar presupuesto
    A->>CRM: Enviar presupuesto
    CRM->>Email: Enviar email
    Email->>C: Presupuesto
    
    C->>A: Acepta presupuesto
    A->>CRM: Actualizar estado
    CRM->>DB: Convertir a contratado
    CRM->>DB: Crear servicio
    
    A->>CRM: Registrar pago
    CRM->>DB: Guardar pago
    CRM->>DB: Actualizar totales
    
    A->>CRM: Subir factura PDF
    CRM->>IA: Procesar PDF
    IA->>CRM: Datos extraídos
    A->>CRM: Validar datos
    CRM->>DB: Guardar factura
```

## 8. Almacenes de Datos

### 8.1 D1 - Clientes
- **Entradas**: Datos personales, información de contacto
- **Salidas**: Información para comunicaciones, expedientes
- **Contenido**: ID, datos personales, teléfonos, emails

### 8.2 D2 - Clientes Contratados
- **Entradas**: Conversión desde clientes regulares
- **Salidas**: Información para servicios y pagos
- **Contenido**: Hereda de clientes + servicios contratados

### 8.3 D3 - Presupuestos
- **Entradas**: Datos del presupuesto, PDF
- **Salidas**: Información para servicios
- **Contenido**: Precios, fechas, estados, enlaces PDF

### 8.4 D4 - Servicios
- **Entradas**: Servicios creados desde presupuestos o comunicaciones
- **Salidas**: Información para pagos y facturas
- **Contenido**: Tipos, fechas, precios, estados

### 8.5 D5 - Comunicaciones
- **Entradas**: Interacciones con clientes
- **Salidas**: Servicios académicos, seguimiento
- **Contenido**: Fechas, medios, textos, resúmenes

### 8.6 D6 - Gestión Servicios
- **Entradas**: Servicios regulares con fechas y precios
- **Salidas**: Control de pagos y facturación
- **Contenido**: Fechas inicio/fin, precios, totales

### 8.7 D7 - Servicios Académicos
- **Entradas**: Servicios menores desde comunicaciones
- **Salidas**: Facturación por tiempo
- **Contenido**: Textos de comunicación, tiempo dedicado

### 8.8 D8 - Pagos
- **Entradas**: Pagos únicos y fraccionados
- **Salidas**: Cálculo de totales
- **Contenido**: Cantidades, estados, fechas, formas de pago

### 8.9 D9 - Plazos
- **Entradas**: Fraccionamiento de pagos
- **Salidas**: Control individual de plazos
- **Contenido**: Números de plazo, cantidades, estados

### 8.10 D10 - Facturas
- **Entradas**: PDFs procesados con IA
- **Salidas**: Documentación de servicios
- **Contenido**: Datos extraídos, enlaces PDF, servicios vinculados
