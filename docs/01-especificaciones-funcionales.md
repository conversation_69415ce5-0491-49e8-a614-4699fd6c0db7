# Especificaciones Funcionales - CRM Personalizado

## 1. Visión General del Sistema

### 1.1 Propósito
Sistema CRM especializado para empresas de asesoría académica a profesores universitarios que buscan promoción profesional.

### 1.2 Alcance
- Gestión integral de clientes potenciales y contratados
- Seguimiento de comunicaciones multicanal
- Gestión de expedientes y presupuestos
- Control de servicios y pagos
- Facturación y seguimiento financiero

## 2. Actores del Sistema

### 2.1 Actores Principales
- **Usuario Administrativo**: Gestiona clientes, servicios y comunicaciones
- **Cliente**: Persona que solicita servicios de asesoría académica
- **Cliente Contratado**: Cliente que ha aceptado un presupuesto o servicio

### 2.2 Actores Secundarios
- **Sistema de IA**: Procesa PDFs de facturas y presupuestos
- **Sistema de Email**: Envía presupuestos y comunicaciones

## 3. Casos de Uso Principales

### 3.1 CU-001: Registro de Cliente Inicial
**Actor Principal**: Usuario Administrativo
**Precondiciones**: Cliente contacta por primera vez
**Flujo Principal**:
1. Usuario registra información básica del cliente
2. Sistema crea registro de comunicación inicial
3. Sistema abre gestión de expediente
4. Usuario documenta detalles de la consulta

**Postcondiciones**: Cliente registrado con expediente abierto

### 3.2 CU-002: Gestión de Presupuesto
**Actor Principal**: Usuario Administrativo
**Precondiciones**: Cliente registrado con expediente
**Flujo Principal**:
1. Usuario crea presupuesto en el expediente
2. Sistema genera enlace para PDF del presupuesto
3. Usuario envía presupuesto por email
4. Sistema registra envío y estado "Enviado"

**Flujos Alternativos**:
- 4a. Cliente acepta: Estado cambia a "Aceptado", cliente pasa a contratado
- 4b. Cliente rechaza: Estado cambia a "Rechazado"

### 3.3 CU-003: Conversión a Cliente Contratado
**Actor Principal**: Sistema (automático)
**Precondiciones**: Cliente acepta presupuesto o servicio académico
**Flujo Principal**:
1. Sistema cambia tipo de cliente a "Contratado"
2. Sistema crea gestión de servicio automáticamente
3. Sistema habilita funcionalidades de pagos
4. Sistema mantiene vinculación con expediente original

### 3.4 CU-004: Gestión de Servicios Académicos
**Actor Principal**: Usuario Administrativo
**Precondiciones**: Cliente solicita servicios menores
**Flujo Principal**:
1. Usuario marca checkbox "Servicio Académico" en comunicación
2. Sistema crea automáticamente entrada de Servicio Académico
3. Si cliente no es contratado, sistema lo convierte automáticamente
4. Sistema vincula servicio con comunicación

### 3.5 CU-005: Gestión de Pagos
**Actor Principal**: Usuario Administrativo
**Precondiciones**: Cliente contratado con servicio activo
**Flujo Principal**:
1. Usuario registra pago (único o primer plazo)
2. Sistema actualiza estado del servicio
3. Sistema calcula montos pagados y pendientes
4. Para pagos fraccionados, sistema gestiona plazos individuales

**Reglas de Negocio**:
- Pago anticipado: Antes del inicio del servicio
- Pago intermedio: Durante la prestación del servicio
- Pago final: Al completar el servicio

### 3.6 CU-006: Procesamiento de Facturas con IA
**Actor Principal**: Usuario Administrativo
**Precondiciones**: Servicio completado
**Flujo Principal**:
1. Usuario sube PDF de factura al sistema
2. Sistema de IA extrae datos automáticamente
3. Sistema popula campos de la factura
4. Usuario valida y confirma información
5. Sistema vincula factura con servicios correspondientes

## 4. Requisitos Funcionales

### 4.1 RF-001: Gestión de Clientes
- El sistema debe permitir registro completo de datos del cliente
- Debe soportar múltiples teléfonos y emails por cliente
- Debe mantener historial completo de comunicaciones
- Debe permitir conversión automática a cliente contratado

### 4.2 RF-002: Gestión de Comunicaciones
- Soporte para múltiples canales: teléfono, WhatsApp, email
- Vinculación automática con expedientes o servicios
- Registro de fecha, hora y resumen de cada comunicación
- Búsqueda y filtrado por cliente, fecha y tipo

### 4.3 RF-003: Gestión de Expedientes
- Creación automática al registrar cliente
- Vinculación con presupuestos y comunicaciones
- Seguimiento de estado del expediente
- Historial completo de actividades

### 4.4 RF-004: Gestión de Presupuestos
- Creación y envío automático por email
- Estados: Generado, Enviado, Aceptado, Rechazado
- Vinculación con tipos de servicios específicos
- Integración con IA para extracción de datos de PDF

### 4.5 RF-005: Gestión de Servicios
- Dos tipos: Gestión de Servicio y Servicio Académico
- Control de fechas de inicio y finalización
- Seguimiento de precios y pagos
- Cálculo automático de montos pendientes

### 4.6 RF-006: Gestión de Pagos
- Soporte para pagos únicos y fraccionados
- Estados: Pendiente, Ordenado, Pagado
- Múltiples formas de pago: Bizum, Transferencia, Efectivo
- Cálculo automático de totales y pendientes

### 4.7 RF-007: Gestión de Facturas
- Subida de PDFs al sistema
- Extracción automática de datos con IA
- Vinculación con múltiples servicios
- Seguimiento de estado de facturación

## 5. Requisitos No Funcionales

### 5.1 RNF-001: Rendimiento
- Tiempo de respuesta < 2 segundos para operaciones básicas
- Soporte para al menos 1000 clientes concurrentes
- Procesamiento de IA < 30 segundos por documento

### 5.2 RNF-002: Seguridad
- Autenticación de usuarios obligatoria
- Encriptación de datos sensibles
- Logs de auditoría para todas las operaciones
- Backup automático diario

### 5.3 RNF-003: Usabilidad
- Interfaz responsive para dispositivos móviles
- Navegación intuitiva con máximo 3 clics
- Formularios con validación en tiempo real
- Mensajes de error claros y específicos

### 5.4 RNF-004: Mantenibilidad
- Código documentado siguiendo estándares PSR
- Arquitectura modular basada en DDD
- Tests unitarios con cobertura > 80%
- Logs estructurados para debugging

## 6. Reglas de Negocio

### 6.1 RN-001: Conversión de Clientes
- Un cliente se convierte automáticamente en contratado al aceptar presupuesto
- La conversión debe ser reversible manualmente
- Servicios académicos convierten automáticamente a contratado

### 6.2 RN-002: Estados de Pago
- Un pago solo se marca como realizado manualmente
- Pagos fraccionados se marcan completos cuando todos los plazos están pagados
- Fecha de pago se asigna automáticamente al marcar como pagado

### 6.3 RN-003: Servicios Académicos
- No requieren presupuesto previo
- Se facturan por tiempo dedicado
- Se crean desde formulario de comunicación
- Tipo de servicio siempre es "Académico"

### 6.4 RN-004: Vinculaciones
- Toda comunicación debe estar vinculada a expediente o servicio
- Presupuestos se vinculan a expedientes
- Facturas se vinculan a uno o múltiples servicios
- Pagos se vinculan exclusivamente a servicios
