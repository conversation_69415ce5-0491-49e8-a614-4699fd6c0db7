# Configuración Apache - CRM Personalizado

## 🌐 Configuración Completa con Apache

### **Ventajas de Apache sobre Nginx para nuestro proyecto:**
✅ **Simplicidad**: Configuración más directa para PHP
✅ **mod_rewrite**: Soporte nativo para URL amigables de Symfony
✅ **Flexibilidad**: Configuración por directorio con .htaccess
✅ **Debugging**: Logs más detallados y fáciles de interpretar
✅ **Ecosistema PHP**: Integración tradicional y bien documentada

## 🐳 Docker Compose Actualizado

```yaml
version: '3.8'

services:
  apache:
    build:
      context: .
      dockerfile: docker/apache/Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/var/www/html
      - ./docker/apache/000-default.conf:/etc/apache2/sites-available/000-default.conf
      - ./docker/apache/apache2.conf:/etc/apache2/apache2.conf
      - ./docker/apache/php.ini:/usr/local/etc/php/php.ini
    environment:
      - APP_ENV=dev
      - DATABASE_URL=mysql://crm_user:crm_pass@mariadb:3306/crm_db
      - REDIS_URL=redis://redis:6379
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    depends_on:
      - mariadb
      - redis

  mariadb:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: crm_db
      MYSQL_USER: crm_user
      MYSQL_PASSWORD: crm_pass
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./docker/mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  node:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./frontend:/app
    command: npm run dev
    ports:
      - "5173:5173"

volumes:
  mariadb_data:
  redis_data:
```

## 📁 Estructura de Archivos Apache

```
docker/
├── apache/
│   ├── Dockerfile
│   ├── 000-default.conf
│   ├── apache2.conf
│   └── php.ini
└── mariadb/
    └── init.sql
```

## ⚙️ Configuraciones Detalladas

### **1. Dockerfile (docker/apache/Dockerfile)**

```dockerfile
FROM php:8.3-apache

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    libicu-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-configure intl \
    && docker-php-ext-install \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        intl \
        opcache

# Instalar extensiones adicionales
RUN pecl install redis \
    && docker-php-ext-enable redis

# Habilitar módulos Apache necesarios
RUN a2enmod rewrite ssl headers deflate expires

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar directorio de trabajo
WORKDIR /var/www/html

# Configurar DocumentRoot dinámico
ENV APACHE_DOCUMENT_ROOT=/var/www/html/public
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

# Copiar archivos de configuración PHP
COPY docker/apache/php.ini /usr/local/etc/php/php.ini

# Instalar dependencias de Composer
COPY composer.json composer.lock ./
RUN composer install --no-scripts --no-autoloader --no-dev

# Copiar código fuente
COPY . .

# Optimizar autoloader
RUN composer dump-autoload --optimize

# Configurar permisos
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/var

# Crear directorio para uploads
RUN mkdir -p /var/www/html/var/uploads \
    && chown -R www-data:www-data /var/www/html/var/uploads

EXPOSE 80 443

# Comando de inicio
CMD ["apache2-foreground"]
```

### **2. Virtual Host (docker/apache/000-default.conf)**

```apache
<VirtualHost *:80>
    ServerName localhost
    ServerAlias crm.local
    DocumentRoot /var/www/html/public
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        
        # Symfony routing - importante para las rutas
        FallbackResource /index.php
        
        # Configuración adicional para archivos estáticos
        <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 month"
        </FilesMatch>
    </Directory>
    
    # Directorio de uploads
    <Directory /var/www/html/var/uploads>
        AllowOverride None
        Require all granted
    </Directory>
    
    # Logs
    ErrorLog ${APACHE_LOG_DIR}/crm_error.log
    CustomLog ${APACHE_LOG_DIR}/crm_access.log combined
    
    # Configuración PHP específica
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value memory_limit 256M
    php_value max_execution_time 300
    
    # Headers de seguridad
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</VirtualHost>

# Virtual Host SSL (para producción)
<VirtualHost *:443>
    ServerName localhost
    ServerAlias crm.local
    DocumentRoot /var/www/html/public
    
    # Configuración SSL
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/localhost.crt
    SSLCertificateKeyFile /etc/ssl/private/localhost.key
    
    # Configuración moderna SSL
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off
    
    # HSTS (opcional)
    Header always set Strict-Transport-Security "max-age=63072000"
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        FallbackResource /index.php
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/crm_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/crm_ssl_access.log combined
</VirtualHost>
```

### **3. Configuración Principal Apache (docker/apache/apache2.conf)**

```apache
# Configuración global
ServerRoot /etc/apache2
PidFile ${APACHE_PID_FILE}
Timeout 300
KeepAlive On
MaxKeepAliveRequests 100
KeepAliveTimeout 5

# Configuración de procesos
StartServers 2
MinSpareServers 2
MaxSpareServers 5
MaxRequestWorkers 150
ThreadsPerChild 25

# Módulos necesarios
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule ssl_module modules/mod_ssl.so
LoadModule headers_module modules/mod_headers.so
LoadModule expires_module modules/mod_expires.so
LoadModule deflate_module modules/mod_deflate.so

# Configuración de compresión
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configuración de caché
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Seguridad
ServerTokens Prod
ServerSignature Off

# Ocultar archivos sensibles
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

<FilesMatch "\.(yml|yaml|ini|log|lock|dist|md|sh|sql)$">
    Require all denied
</FilesMatch>

# Incluir configuraciones adicionales
IncludeOptional conf-enabled/*.conf
IncludeOptional sites-enabled/*.conf
```

### **4. Configuración PHP (docker/apache/php.ini)**

```ini
[PHP]
; Configuración básica
engine = On
short_open_tag = Off
precision = 14
output_buffering = 4096
zlib.output_compression = Off
implicit_flush = Off
serialize_precision = -1

; Límites de recursos
max_execution_time = 300
max_input_time = 60
memory_limit = 256M

; Manejo de errores
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = On
display_startup_errors = On
log_errors = On
error_log = /var/log/php_errors.log

; Uploads
file_uploads = On
upload_max_filesize = 50M
max_file_uploads = 20
post_max_size = 50M

; Configuración de sesiones
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.use_strict_mode = 1
session.cookie_httponly = 1
session.cookie_secure = 0
session.use_only_cookies = 1

; OPcache (importante para rendimiento)
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

; Configuración de fecha
date.timezone = Europe/Madrid

; Extensiones
extension = pdo_mysql
extension = redis
extension = zip
extension = gd
extension = intl
extension = bcmath
extension = opcache
```

## 🚀 Comandos de Gestión

### **Makefile Actualizado**

```makefile
.PHONY: install start stop restart logs test lint

install:
	docker-compose build apache
	docker-compose run --rm apache composer install
	docker-compose run --rm node npm install
	docker-compose up -d
	docker-compose exec apache php bin/console doctrine:database:create --if-not-exists
	docker-compose exec apache php bin/console doctrine:migrations:migrate --no-interaction

start:
	docker-compose up -d

stop:
	docker-compose down

restart:
	docker-compose restart apache

logs:
	docker-compose logs -f apache

logs-error:
	docker-compose exec apache tail -f /var/log/apache2/crm_error.log

test:
	docker-compose exec apache vendor/bin/phpunit
	docker-compose run --rm node npm test

lint:
	docker-compose exec apache vendor/bin/phpstan analyse
	docker-compose exec apache vendor/bin/php-cs-fixer fix --dry-run
	docker-compose run --rm node npm run lint

fix:
	docker-compose exec apache vendor/bin/php-cs-fixer fix

migrate:
	docker-compose exec apache php bin/console doctrine:migrations:migrate --no-interaction

cache-clear:
	docker-compose exec apache php bin/console cache:clear

apache-reload:
	docker-compose exec apache apache2ctl graceful

apache-status:
	docker-compose exec apache apache2ctl status
```

## 🔧 Configuración de Desarrollo

### **.htaccess para Symfony (public/.htaccess)**

```apache
DirectoryIndex index.php

<IfModule mod_negotiation.c>
    Options -MultiViews
</IfModule>

<IfModule mod_rewrite.c>
    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect to URI without front controller to prevent duplicate content
    RewriteCond %{ENV:REDIRECT_STATUS} ^$
    RewriteRule ^index\.php(?:/(.*)|$) %{ENV:BASE}/$1 [R=301,L]

    # If the requested filename exists, don't do anything
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule .? - [L]

    # Rewrite all other queries to the front controller
    RewriteRule .? %{ENV:BASE}/index.php [L]
</IfModule>

<IfModule !mod_rewrite.c>
    <IfModule mod_alias.c>
        RedirectMatch 302 ^/$ /index.php/
    </IfModule>
</IfModule>
```

## 📊 Ventajas de esta Configuración

### **✅ Rendimiento:**
- **OPcache habilitado**: Cache de bytecode PHP
- **Compresión gzip**: Reduce tamaño de respuestas
- **Cache de archivos estáticos**: Mejora tiempo de carga
- **KeepAlive**: Reutiliza conexiones TCP

### **✅ Seguridad:**
- **Headers de seguridad**: XSS, CSRF, Clickjacking protection
- **Archivos sensibles ocultos**: .env, logs, configs
- **SSL/TLS moderno**: Protocolos y cifrados seguros
- **Tokens de servidor ocultos**: Reduce información expuesta

### **✅ Desarrollo:**
- **Logs detallados**: Fácil debugging
- **Hot reload**: Cambios inmediatos sin rebuild
- **Configuración flexible**: Fácil personalización
- **Comandos make**: Gestión simplificada

¿Te parece bien esta configuración con Apache? ¿Algún aspecto específico que quieras ajustar?
