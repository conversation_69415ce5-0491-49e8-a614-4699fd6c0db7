# Documentación Técnica - CRM Personalizado

## 📋 Índice de Documentación

Esta documentación técnica completa del sistema CRM personalizado está organizada en los siguientes documentos:

### 0. [Contexto del Proyecto](./00-contexto-proyecto.md)
- **Propósito**: Establecer el contexto empresarial y justificación del proyecto
- **Contenido**:
  - Descripción de la empresa y sector
  - Problemática identificada y análisis de necesidades
  - Solución propuesta y beneficios esperados
  - Análisis de impacto y plan de implementación

### 1. [Especificaciones Funcionales](./01-especificaciones-funcionales.md)
- **Propósito**: Definición detallada de requisitos funcionales y casos de uso
- **Contenido**:
  - Visión general del sistema
  - Actores principales y secundarios
  - Casos de uso detallados (CU-001 a CU-006)
  - Requisitos funcionales (RF-001 a RF-007)
  - Requisitos no funcionales
  - Reglas de negocio

### 2. [Diagramas de Flujo de Datos](./02-diagramas-flujo-datos.md)
- **Propósito**: Visualización del flujo de información en el sistema
- **Contenido**:
  - Diagrama de contexto (Nivel 0)
  - Diagramas de nivel 1 y 2
  - Procesos principales del sistema
  - Almacenes de datos
  - Flujos de información entre componentes

### 3. [Diagramas de Actividad y Secuencia](./03-diagramas-actividad-secuencia.md)
- **Propósito**: Modelado de procesos de negocio e interacciones
- **Contenido**:
  - Diagramas de actividad para procesos clave
  - Diagramas de secuencia para interacciones
  - Estados y transiciones del sistema
  - Flujos de trabajo completos

### 4. [Modelo de Datos DDD](./04-modelo-datos-ddd.md)
- **Propósito**: Diseño del modelo de dominio orientado a DDD
- **Contenido**:
  - Arquitectura de dominios y bounded contexts
  - Agregados, entidades y value objects
  - Domain events y repositorios
  - Implementación en PHP con tipado fuerte

### 5. [Arquitectura del Sistema](./05-arquitectura-sistema.md)
- **Propósito**: Definición de la arquitectura técnica
- **Contenido**:
  - Patrón hexagonal (Ports & Adapters)
  - Estructura de directorios DDD
  - Capas de aplicación, dominio e infraestructura
  - Patrones CQRS y Event Sourcing

### 6. [Especificaciones Técnicas](./06-especificaciones-tecnicas.md)
- **Propósito**: Detalles de implementación y configuración
- **Contenido**:
  - Stack tecnológico completo
  - Configuración de Docker y entorno
  - Schema de base de datos
  - Configuración de Symfony y React

### 7. [Configuración Apache](./07-configuracion-apache.md)
- **Propósito**: Configuración específica del servidor web Apache
- **Contenido**:
  - Docker Compose con Apache
  - Virtual Hosts y configuración SSL
  - Optimizaciones de rendimiento
  - Configuración PHP y módulos

## 🎯 Resumen Ejecutivo

### Características Principales del Sistema

#### **Enfoque Domain-Driven Design (DDD)**
- **4 Bounded Contexts**: Cliente, Gestión, Servicio, Financiero
- **Arquitectura Hexagonal**: Separación clara entre dominio, aplicación e infraestructura
- **Agregados y Entidades**: Modelado rico del dominio con reglas de negocio encapsuladas
- **Domain Events**: Comunicación asíncrona entre contextos

#### **Stack Tecnológico Moderno**
- **Backend**: PHP 8.3 + Symfony 6.4 + Doctrine ORM
- **Frontend**: React 18 + TypeScript + Material-UI
- **Base de Datos**: MariaDB 10.11 con índices optimizados
- **Infraestructura**: Docker + Apache + Redis

#### **Funcionalidades Clave**
1. **Gestión Integral de Clientes**
   - Registro y seguimiento de clientes potenciales
   - Conversión automática a clientes contratados
   - Historial completo de comunicaciones

2. **Gestión de Servicios Duales**
   - **Servicios Regulares**: Con presupuesto, fechas y pagos estructurados
   - **Servicios Académicos**: Servicios menores sin presupuesto previo

3. **Sistema de Pagos Flexible**
   - Pagos únicos y fraccionados
   - Estados: Pendiente, Ordenado, Pagado
   - Cálculo automático de totales y pendientes

4. **Procesamiento Inteligente con IA**
   - Extracción automática de datos de PDFs
   - Validación manual de información extraída
   - Integración con OpenAI API

5. **Comunicaciones Multicanal**
   - Soporte para email, teléfono y WhatsApp
   - Vinculación automática con expedientes y servicios
   - Creación automática de servicios académicos

## 🏗️ Arquitectura de Alto Nivel

```mermaid
graph TB
    subgraph "Frontend - React"
        UI[Interfaz de Usuario]
        COMP[Componentes]
        SRV[Servicios]
    end
    
    subgraph "Backend - Symfony"
        API[REST API]
        APP[Capa Aplicación]
        DOM[Capa Dominio]
        INF[Capa Infraestructura]
    end
    
    subgraph "Datos"
        DB[(MariaDB)]
        REDIS[(Redis)]
        FILES[Almacén PDFs]
    end
    
    subgraph "Servicios Externos"
        EMAIL[Servicio Email]
        IA[OpenAI API]
    end
    
    UI --> API
    API --> APP
    APP --> DOM
    DOM --> INF
    INF --> DB
    INF --> REDIS
    INF --> FILES
    INF --> EMAIL
    INF --> IA
```

## 🚀 Flujo de Trabajo Principal

### 1. **Registro de Cliente**
```
Cliente contacta → Registro en sistema → Creación expediente → Registro comunicación
```

### 2. **Proceso con Presupuesto**
```
Crear presupuesto → Enviar por email → Cliente acepta → Conversión a contratado → Crear servicio → Gestionar pagos → Generar factura
```

### 3. **Proceso Servicio Académico**
```
Comunicación cliente → Marcar como académico → Conversión automática → Crear servicio → Trabajar → Facturar conjunto
```

## 📊 Dominios del Sistema

### **Dominio Cliente**
- **Agregado**: Cliente (con comunicaciones)
- **Responsabilidades**: Gestión de datos personales, comunicaciones, conversión a contratado

### **Dominio Gestión**
- **Agregados**: Expediente, Presupuesto
- **Responsabilidades**: Gestión de expedientes, creación y seguimiento de presupuestos

### **Dominio Servicio**
- **Agregados**: GestionServicio, ServicioAcademico
- **Responsabilidades**: Gestión de servicios regulares y académicos, control de fechas y precios

### **Dominio Financiero**
- **Agregados**: Pago, Factura
- **Responsabilidades**: Gestión de pagos únicos/fraccionados, procesamiento de facturas con IA

## 🔧 Configuración y Deployment

### **Requisitos del Sistema**
- Docker y Docker Compose
- PHP 8.3+
- Node.js 18+
- MariaDB 10.11+
- Redis 7+

### **Comandos Principales**
```bash
# Instalación completa
make install

# Iniciar servicios
make start

# Ejecutar tests
make test

# Análisis de código
make lint

# Migraciones de BD
make migrate
```

### **Variables de Entorno**
```env
APP_ENV=dev
DATABASE_URL=mysql://user:pass@mariadb:3306/crm_db
REDIS_URL=redis://redis:6379
OPENAI_API_KEY=your_openai_key
FROM_EMAIL=<EMAIL>
```

## 📈 Métricas de Calidad

### **Cobertura de Tests**
- **Objetivo**: >80% cobertura de código
- **Tests Unitarios**: Dominio y aplicación
- **Tests de Integración**: Repositorios y servicios
- **Tests E2E**: Flujos completos de usuario

### **Análisis Estático**
- **PHPStan Level 8**: Análisis estático máximo
- **PHP-CS-Fixer**: Estándares PSR-12 y Symfony
- **ESLint**: Análisis de TypeScript/React

### **Rendimiento**
- **Tiempo de respuesta**: <2 segundos operaciones básicas
- **Procesamiento IA**: <30 segundos por documento
- **Concurrencia**: 1000+ usuarios simultáneos

## 🔐 Seguridad

### **Medidas Implementadas**
- Autenticación obligatoria de usuarios
- Validación de entrada en todas las capas
- Encriptación de datos sensibles
- Logs de auditoría completos
- Backup automático diario

### **Buenas Prácticas**
- Principios SOLID en todo el código
- Tipado fuerte en PHP y TypeScript
- Validación con Symfony Validator y Zod
- Sanitización de datos de entrada

## 📚 Próximos Pasos

1. **Revisión de Documentación**: Validar especificaciones con stakeholders
2. **Setup del Entorno**: Configurar Docker y dependencias
3. **Implementación por Dominios**: Comenzar con dominio Cliente
4. **Tests Unitarios**: Implementar tests para cada agregado
5. **Integración Frontend**: Desarrollar componentes React
6. **Testing E2E**: Validar flujos completos
7. **Deployment**: Configurar entorno de producción

---

**Nota**: Esta documentación está diseñada para ser un documento vivo que evolucione con el proyecto. Se recomienda mantenerla actualizada conforme se implementen cambios en el sistema.
