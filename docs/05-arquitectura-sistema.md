# Arquitectura del Sistema - CRM Personalizado

## 1. Arquitectura General

### 1.1 Patrón Arquitectónico: Hexagonal (Ports & Adapters)

```mermaid
graph TB
    subgraph "Capa de Presentación"
        UI[React Frontend]
        API[REST API]
    end
    
    subgraph "Capa de Aplicación"
        UC[Use Cases]
        CMD[Command Handlers]
        QRY[Query Handlers]
        EVT[Event Handlers]
    end
    
    subgraph "Capa de Dominio"
        AGG[Aggregates]
        ENT[Entities]
        VO[Value Objects]
        SRV[Domain Services]
        EVD[Domain Events]
    end
    
    subgraph "Capa de Infraestructura"
        REPO[Repositories]
        DB[(MariaDB)]
        EMAIL[Email Service]
        IA[IA Service]
        FILE[File Storage]
    end
    
    UI --> API
    API --> UC
    UC --> CMD
    UC --> QRY
    CMD --> AGG
    QRY --> REPO
    AGG --> EVD
    EVD --> EVT
    REPO --> DB
    UC --> EMAIL
    UC --> IA
    UC --> FILE
```

### 1.2 Estructura de Directorios DDD

```
src/
├── Application/
│   ├── Command/
│   │   ├── Cliente/
│   │   ├── Expediente/
│   │   ├── Servicio/
│   │   ├── Pago/
│   │   └── Factura/
│   ├── Query/
│   │   ├── Cliente/
│   │   ├── Expediente/
│   │   ├── Servicio/
│   │   ├── Pago/
│   │   └── Factura/
│   ├── EventHandler/
│   └── Service/
├── Domain/
│   ├── Cliente/
│   │   ├── Entity/
│   │   ├── ValueObject/
│   │   ├── Repository/
│   │   ├── Service/
│   │   └── Event/
│   ├── Gestion/
│   │   ├── Expediente/
│   │   └── Presupuesto/
│   ├── Servicio/
│   │   ├── GestionServicio/
│   │   └── ServicioAcademico/
│   ├── Financiero/
│   │   ├── Pago/
│   │   └── Factura/
│   └── Shared/
│       ├── ValueObject/
│       ├── Event/
│       └── Exception/
├── Infrastructure/
│   ├── Persistence/
│   │   ├── MariaDB/
│   │   └── Repository/
│   ├── External/
│   │   ├── Email/
│   │   ├── IA/
│   │   └── FileStorage/
│   ├── Web/
│   │   ├── Controller/
│   │   ├── Middleware/
│   │   └── Request/
│   └── Console/
└── UI/
    ├── Components/
    ├── Pages/
    ├── Services/
    └── Utils/
```

## 2. Capa de Aplicación

### 2.1 Command Pattern - Casos de Uso

```php
// Command
class CrearClienteCommand
{
    public function __construct(
        public readonly string $idManual,
        public readonly string $nif,
        public readonly string $nombre,
        public readonly string $apellidos,
        public readonly array $telefonos,
        public readonly array $emails
    ) {}
}

// Command Handler
class CrearClienteCommandHandler
{
    public function __construct(
        private ClienteRepository $clienteRepository,
        private EventDispatcher $eventDispatcher
    ) {}
    
    public function handle(CrearClienteCommand $command): ClienteId
    {
        // Validar que no existe cliente con mismo NIF
        $existeCliente = $this->clienteRepository->findByNif(
            new Nif($command->nif)
        );
        
        if ($existeCliente) {
            throw new ClienteYaExisteException();
        }
        
        // Crear cliente
        $cliente = Cliente::crear(
            ClienteId::generate(),
            $command->idManual,
            new Nif($command->nif),
            new NombreCompleto($command->nombre, $command->apellidos),
            new ContactInfo($command->telefonos, $command->emails)
        );
        
        // Persistir
        $this->clienteRepository->save($cliente);
        
        // Disparar eventos
        $this->eventDispatcher->dispatch($cliente->pullDomainEvents());
        
        return $cliente->getId();
    }
}
```

### 2.2 Query Pattern - Consultas

```php
// Query
class ObtenerClientesQuery
{
    public function __construct(
        public readonly ?string $filtroNombre = null,
        public readonly ?string $filtroNif = null,
        public readonly ?TipoCliente $filtroTipo = null,
        public readonly int $pagina = 1,
        public readonly int $limite = 20
    ) {}
}

// Query Handler
class ObtenerClientesQueryHandler
{
    public function __construct(
        private ClienteReadRepository $clienteReadRepository
    ) {}
    
    public function handle(ObtenerClientesQuery $query): ClienteCollection
    {
        return $this->clienteReadRepository->findWithFilters(
            $query->filtroNombre,
            $query->filtroNif,
            $query->filtroTipo,
            $query->pagina,
            $query->limite
        );
    }
}
```

### 2.3 Event Handlers

```php
class ClienteConvertidoAContratadoEventHandler
{
    public function __construct(
        private ServicioService $servicioService,
        private ClienteRepository $clienteRepository
    ) {}
    
    public function handle(ClienteConvertidoAContratado $event): void
    {
        $cliente = $this->clienteRepository->findById($event->getClienteId());
        
        if (!$cliente) {
            throw new ClienteNoEncontradoException();
        }
        
        // Crear servicios automáticamente si es necesario
        $this->servicioService->crearServiciosIniciales($cliente);
    }
}
```

## 3. Capa de Infraestructura

### 3.1 Repositorios con Doctrine ORM

```php
class DoctrineClienteRepository implements ClienteRepository
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {}
    
    public function save(Cliente $cliente): void
    {
        $this->entityManager->persist($cliente);
        $this->entityManager->flush();
    }
    
    public function findById(ClienteId $id): ?Cliente
    {
        return $this->entityManager->find(Cliente::class, $id->getValue());
    }
    
    public function findByNif(Nif $nif): ?Cliente
    {
        return $this->entityManager
            ->getRepository(Cliente::class)
            ->findOneBy(['nif.value' => $nif->getValue()]);
    }
}
```

### 3.2 Servicios Externos

```php
// Email Service
interface EmailServiceInterface
{
    public function enviarPresupuesto(Email $destinatario, string $pdfPath): bool;
    public function enviarFactura(Email $destinatario, string $pdfPath): bool;
}

class SymfonyMailerEmailService implements EmailServiceInterface
{
    public function __construct(
        private MailerInterface $mailer,
        private string $fromEmail
    ) {}
    
    public function enviarPresupuesto(Email $destinatario, string $pdfPath): bool
    {
        $email = (new TemplatedEmail())
            ->from($this->fromEmail)
            ->to($destinatario->getValue())
            ->subject('Presupuesto de Servicios Académicos')
            ->htmlTemplate('emails/presupuesto.html.twig')
            ->attachFromPath($pdfPath);
            
        try {
            $this->mailer->send($email);
            return true;
        } catch (TransportExceptionInterface $e) {
            return false;
        }
    }
}

// IA Service
interface IAServiceInterface
{
    public function extraerDatosFactura(string $pdfPath): array;
    public function extraerDatosPresupuesto(string $pdfPath): array;
}

class OpenAIService implements IAServiceInterface
{
    public function __construct(
        private string $apiKey,
        private HttpClientInterface $httpClient
    ) {}
    
    public function extraerDatosFactura(string $pdfPath): array
    {
        // Implementación con OpenAI API
        $pdfContent = $this->convertPdfToText($pdfPath);
        
        $response = $this->httpClient->request('POST', 'https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-4',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Extrae los datos de esta factura en formato JSON...'
                    ],
                    [
                        'role' => 'user',
                        'content' => $pdfContent
                    ]
                ]
            ]
        ]);
        
        return json_decode($response->getContent(), true);
    }
}
```

## 4. Capa de Presentación

### 4.1 Controladores REST

```php
#[Route('/api/clientes')]
class ClienteController extends AbstractController
{
    public function __construct(
        private CommandBus $commandBus,
        private QueryBus $queryBus
    ) {}
    
    #[Route('', methods: ['POST'])]
    public function crear(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        $command = new CrearClienteCommand(
            $data['id_manual'],
            $data['nif'],
            $data['nombre'],
            $data['apellidos'],
            $data['telefonos'],
            $data['emails']
        );
        
        try {
            $clienteId = $this->commandBus->handle($command);
            
            return new JsonResponse([
                'id' => $clienteId->getValue(),
                'message' => 'Cliente creado exitosamente'
            ], 201);
        } catch (DomainException $e) {
            return new JsonResponse([
                'error' => $e->getMessage()
            ], 400);
        }
    }
    
    #[Route('', methods: ['GET'])]
    public function listar(Request $request): JsonResponse
    {
        $query = new ObtenerClientesQuery(
            $request->query->get('nombre'),
            $request->query->get('nif'),
            $request->query->get('tipo') ? TipoCliente::from($request->query->get('tipo')) : null,
            (int) $request->query->get('pagina', 1),
            (int) $request->query->get('limite', 20)
        );
        
        $clientes = $this->queryBus->handle($query);
        
        return new JsonResponse($clientes->toArray());
    }
}
```

### 4.2 Frontend React

```typescript
// Cliente Service
class ClienteService {
    private baseUrl = '/api/clientes';
    
    async crear(clienteData: CrearClienteRequest): Promise<Cliente> {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(clienteData),
        });
        
        if (!response.ok) {
            throw new Error('Error al crear cliente');
        }
        
        return response.json();
    }
    
    async listar(filtros?: ClienteFiltros): Promise<Cliente[]> {
        const params = new URLSearchParams();
        if (filtros?.nombre) params.append('nombre', filtros.nombre);
        if (filtros?.nif) params.append('nif', filtros.nif);
        if (filtros?.tipo) params.append('tipo', filtros.tipo);
        
        const response = await fetch(`${this.baseUrl}?${params}`);
        return response.json();
    }
}

// React Component
const ClienteForm: React.FC = () => {
    const [formData, setFormData] = useState<CrearClienteRequest>({
        id_manual: '',
        nif: '',
        nombre: '',
        apellidos: '',
        telefonos: [''],
        emails: ['']
    });
    
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        try {
            await clienteService.crear(formData);
            // Mostrar mensaje de éxito
            // Limpiar formulario
        } catch (error) {
            // Mostrar mensaje de error
        }
    };
    
    return (
        <form onSubmit={handleSubmit}>
            {/* Campos del formulario */}
        </form>
    );
};
```

## 5. Configuración y Dependencias

### 5.1 Dependency Injection (Symfony)

```yaml
# config/services.yaml
services:
    _defaults:
        autowire: true
        autoconfigure: true
    
    # Repositories
    App\Domain\Cliente\Repository\ClienteRepository:
        class: App\Infrastructure\Persistence\Doctrine\DoctrineClienteRepository
    
    # Services
    App\Infrastructure\External\Email\EmailServiceInterface:
        class: App\Infrastructure\External\Email\SymfonyMailerEmailService
        arguments:
            $fromEmail: '%env(FROM_EMAIL)%'
    
    App\Infrastructure\External\IA\IAServiceInterface:
        class: App\Infrastructure\External\IA\OpenAIService
        arguments:
            $apiKey: '%env(OPENAI_API_KEY)%'
    
    # Command Bus
    App\Application\Command\CommandBus:
        class: App\Infrastructure\Bus\SymfonyCommandBus
    
    # Query Bus
    App\Application\Query\QueryBus:
        class: App\Infrastructure\Bus\SymfonyQueryBus
```

### 5.2 Base de Datos (Doctrine Mapping)

```yaml
# config/doctrine/Cliente.orm.yaml
App\Domain\Cliente\Entity\Cliente:
    type: entity
    table: clientes
    id:
        id:
            type: cliente_id
            generator:
                strategy: NONE
    fields:
        idManual:
            type: string
            length: 50
            unique: true
            nullable: false
        tipo:
            type: string
            enumType: App\Domain\Cliente\ValueObject\TipoCliente
    embedded:
        nif:
            class: App\Domain\Cliente\ValueObject\Nif
            columnPrefix: false
        nombreCompleto:
            class: App\Domain\Cliente\ValueObject\NombreCompleto
            columnPrefix: false
        contactInfo:
            class: App\Domain\Cliente\ValueObject\ContactInfo
            columnPrefix: false
    oneToMany:
        expedientes:
            targetEntity: App\Domain\Gestion\Entity\Expediente
            mappedBy: clienteId
            cascade: [persist, remove]
        comunicaciones:
            targetEntity: App\Domain\Cliente\Entity\Comunicacion
            mappedBy: clienteId
            cascade: [persist, remove]
```

## 6. Testing Strategy

### 6.1 Estructura de Tests

```
tests/
├── Unit/
│   ├── Domain/
│   │   ├── Cliente/
│   │   ├── Servicio/
│   │   └── Financiero/
│   └── Application/
├── Integration/
│   ├── Repository/
│   └── Service/
├── Functional/
│   └── Controller/
└── E2E/
    └── Features/
```

### 6.2 Test Unitario de Dominio

```php
class ClienteTest extends TestCase
{
    public function test_puede_crear_cliente_valido(): void
    {
        $cliente = Cliente::crear(
            new ClienteId(1),
            'CLI-001',
            new Nif('12345678Z'),
            new NombreCompleto('Juan', 'Pérez'),
            new ContactInfo(['123456789'], ['<EMAIL>'])
        );
        
        $this->assertEquals('CLI-001', $cliente->getIdManual());
        $this->assertEquals(TipoCliente::POTENCIAL, $cliente->getTipo());
        $this->assertCount(1, $cliente->pullDomainEvents());
    }
    
    public function test_conversion_a_contratado_dispara_evento(): void
    {
        $cliente = $this->crearClienteValido();
        
        $cliente->convertirAContratado();
        
        $this->assertEquals(TipoCliente::CONTRATADO, $cliente->getTipo());
        
        $eventos = $cliente->pullDomainEvents();
        $this->assertInstanceOf(
            ClienteConvertidoAContratado::class,
            $eventos[1] // El primer evento es ClienteCreado
        );
    }
}
```
