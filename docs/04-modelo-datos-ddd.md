# Modelo de Datos DDD - CRM Personalizado

## 1. Arquitectura de Dominios

### 1.1 Bounded Contexts Identificados

```mermaid
graph TB
    subgraph "CRM System"
        subgraph "Cliente Context"
            CC[Cliente Core]
            CM[Comunicaciones]
        end
        
        subgraph "Gestión Context"
            EX[Expedientes]
            PR[Presupuestos]
        end
        
        subgraph "Servicio Context"
            SV[Servicios]
            SA[Servicios Académicos]
        end
        
        subgraph "Financiero Context"
            PG[Pagos]
            FC[Facturas]
        end
        
        subgraph "Shared Kernel"
            VO[Value Objects]
            EV[Domain Events]
        end
    end
    
    CC --> EX
    EX --> PR
    PR --> SV
    SV --> PG
    SV --> FC
    CM --> SA
```

## 2. Dominio Cliente

### 2.1 Agregado Cliente

```php
// Cliente Aggregate Root
class Cliente implements AggregateRoot
{
    private ClienteId $id;
    private string $idManual;
    private Nif $nif;
    private NombreCompleto $nombreCompleto;
    private ContactInfo $contactInfo;
    private TipoCliente $tipo;
    private Collection $expedientes;
    private Collection $comunicaciones;
    
    // Domain Events
    private array $domainEvents = [];
}

// Value Objects
class ClienteId
{
    private int $value;
}

class Nif
{
    private string $value;
    
    public function __construct(string $nif)
    {
        $this->validate($nif);
        $this->value = $nif;
    }
}

class NombreCompleto
{
    private string $nombre;
    private string $apellidos;
}

class ContactInfo
{
    private array $telefonos; // máximo 3
    private array $emails;    // máximo 3
}

enum TipoCliente: string
{
    case POTENCIAL = 'potencial';
    case CONTRATADO = 'contratado';
}
```

### 2.2 Entidad Comunicación

```php
class Comunicacion implements Entity
{
    private ComunicacionId $id;
    private ClienteId $clienteId;
    private ?ExpedienteId $expedienteId;
    private ?ServicioId $servicioId;
    private DateTime $fechaHora;
    private MedioComunicacion $medio;
    private string $texto;
    private string $resumen;
    private bool $esServicioAcademico;
}

enum MedioComunicacion: string
{
    case EMAIL = 'email';
    case TELEFONO = 'telefono';
    case WHATSAPP = 'whatsapp';
}
```

## 3. Dominio Gestión

### 3.1 Agregado Expediente

```php
class Expediente implements AggregateRoot
{
    private ExpedienteId $id;
    private ClienteId $clienteId;
    private DateTime $fechaCreacion;
    private string $resumen;
    private ?string $texto;
    private Collection $presupuestos;
    private Collection $comunicaciones;
    
    public function crearPresupuesto(
        array $tiposServicios,
        string $descripcion,
        Money $precio,
        string $enlacePdf
    ): Presupuesto {
        $presupuesto = new Presupuesto(
            PresupuestoId::generate(),
            $this->id,
            $this->clienteId,
            $tiposServicios,
            $descripcion,
            $precio,
            $enlacePdf
        );
        
        $this->presupuestos->add($presupuesto);
        $this->recordEvent(new PresupuestoCreado($presupuesto->getId()));
        
        return $presupuesto;
    }
}
```

### 3.2 Entidad Presupuesto

```php
class Presupuesto implements Entity
{
    private PresupuestoId $id;
    private ExpedienteId $expedienteId;
    private ClienteId $clienteId;
    private DateTime $fecha;
    private array $tiposServicios;
    private string $descripcionConcepto;
    private Money $precio;
    private string $enlacePdf;
    private EstadoPresupuesto $estado;
    
    public function marcarComoEnviado(): void
    {
        $this->estado = EstadoPresupuesto::ENVIADO;
        $this->recordEvent(new PresupuestoEnviado($this->id));
    }
    
    public function aceptar(): void
    {
        if ($this->estado !== EstadoPresupuesto::ENVIADO) {
            throw new DomainException('Solo se pueden aceptar presupuestos enviados');
        }
        
        $this->estado = EstadoPresupuesto::ACEPTADO;
        $this->recordEvent(new PresupuestoAceptado($this->id, $this->clienteId));
    }
}

enum EstadoPresupuesto: string
{
    case GENERADO = 'generado';
    case ENVIADO = 'enviado';
    case ACEPTADO = 'aceptado';
    case RECHAZADO = 'rechazado';
}
```

## 4. Dominio Servicio

### 4.1 Agregado Servicio (Clase Base Abstracta)

```php
abstract class ServicioBase implements AggregateRoot
{
    protected ServicioId $id;
    protected ClienteId $clienteId;
    protected DateTime $fechaCreacion;
    protected TipoServicio $tipo;
    protected string $comentario;
    protected Collection $comunicaciones;
    protected array $domainEvents = [];
    
    abstract public function calcularPrecio(): Money;
    abstract public function puedeFacturarse(): bool;
}

enum TipoServicio: string
{
    case ACREDITACION_TITULAR = 'acreditacion_titular_universitario';
    case ACREDITACION_CATEDRATICO = 'acreditacion_catedratico_universidad';
    case ACREDITACION_CONTRATADO_DOCTOR = 'acreditacion_contratado_doctor';
    case SEXENIO = 'sexenio';
    case GESTION_CURRICULUM = 'gestion_curriculum';
    case CREACION_WEB = 'creacion_pagina_web';
    case ACTUALIZACION_WEB = 'actualizacion_pagina_web';
    case GESTION_INFORMACION = 'gestion_informacion';
    case ACADEMICO = 'academico';
}
```

### 4.2 Entidad Gestión Servicio

```php
class GestionServicio extends ServicioBase
{
    private ?DateTime $fechaInicio;
    private ?DateTime $fechaPrevistaFinalizacion;
    private Money $precio;
    private Money $pagado;
    private Money $pendientePago;
    private Collection $pagos;
    private ?RegistroDemora $demora;
    
    public function __construct(
        ServicioId $id,
        ClienteId $clienteId,
        TipoServicio $tipo,
        Money $precio,
        ?DateTime $fechaInicio = null,
        ?DateTime $fechaFinalizacion = null
    ) {
        parent::__construct($id, $clienteId, $tipo);
        $this->precio = $precio;
        $this->pagado = Money::zero();
        $this->pendientePago = $precio;
        $this->fechaInicio = $fechaInicio;
        $this->fechaPrevistaFinalizacion = $fechaFinalizacion;
        $this->pagos = new Collection();
    }
    
    public function registrarPago(Pago $pago): void
    {
        $this->pagos->add($pago);
        $this->recalcularTotales();
        $this->recordEvent(new PagoRegistrado($this->id, $pago->getId()));
    }
    
    private function recalcularTotales(): void
    {
        $totalPagado = $this->pagos
            ->filter(fn(Pago $pago) => $pago->estaPagado())
            ->sum(fn(Pago $pago) => $pago->getCantidad());
            
        $this->pagado = new Money($totalPagado);
        $this->pendientePago = $this->precio->subtract($this->pagado);
    }
    
    public function registrarDemora(string $motivo, DateTime $nuevaFecha): void
    {
        $this->demora = new RegistroDemora($motivo, $nuevaFecha);
        $this->fechaInicio = $nuevaFecha;
    }
}

class RegistroDemora
{
    private string $motivo;
    private DateTime $nuevaFechaInicio;
    private DateTime $fechaRegistro;
}
```

### 4.3 Entidad Servicio Académico

```php
class ServicioAcademico extends ServicioBase
{
    private string $textoComunicacion;
    private ?Money $precioCalculado = null;
    
    public function __construct(
        ServicioId $id,
        ClienteId $clienteId,
        string $textoComunicacion
    ) {
        parent::__construct($id, $clienteId, TipoServicio::ACADEMICO);
        $this->textoComunicacion = $textoComunicacion;
    }
    
    public function calcularPrecio(): Money
    {
        // Los servicios académicos se calculan por tiempo dedicado
        // Se implementará lógica específica
        return $this->precioCalculado ?? Money::zero();
    }
    
    public function establecerPrecio(Money $precio): void
    {
        $this->precioCalculado = $precio;
    }
    
    public function puedeFacturarse(): bool
    {
        return $this->precioCalculado !== null;
    }
}
```

## 5. Dominio Financiero

### 5.1 Agregado Pago

```php
class Pago implements AggregateRoot
{
    private PagoId $id;
    private ServicioId $servicioId;
    private TipoPago $tipo;
    private ModoPago $modo;
    private string $comentario;
    private Money $cantidad;
    private EstadoPago $estado;
    private ?DateTime $fechaPagado;
    private Collection $plazos;
    private bool $esFraccionado;
    
    public static function crearUnico(
        PagoId $id,
        ServicioId $servicioId,
        TipoPago $tipo,
        ModoPago $modo,
        Money $cantidad
    ): self {
        $pago = new self();
        $pago->id = $id;
        $pago->servicioId = $servicioId;
        $pago->tipo = $tipo;
        $pago->modo = $modo;
        $pago->cantidad = $cantidad;
        $pago->estado = EstadoPago::PENDIENTE;
        $pago->esFraccionado = false;
        $pago->plazos = new Collection();
        
        return $pago;
    }
    
    public static function crearFraccionado(
        PagoId $id,
        ServicioId $servicioId,
        TipoPago $tipo,
        ModoPago $modo,
        array $plazos
    ): self {
        $pago = new self();
        $pago->id = $id;
        $pago->servicioId = $servicioId;
        $pago->tipo = $tipo;
        $pago->modo = $modo;
        $pago->esFraccionado = true;
        $pago->estado = EstadoPago::PENDIENTE;
        
        $totalCantidad = 0;
        foreach ($plazos as $plazoData) {
            $plazo = new PagoPlazo(
                PagoPlazoId::generate(),
                $pago->id,
                $plazoData['numero'],
                new Money($plazoData['cantidad']),
                $plazoData['fecha']
            );
            $pago->plazos->add($plazo);
            $totalCantidad += $plazoData['cantidad'];
        }
        
        $pago->cantidad = new Money($totalCantidad);
        return $pago;
    }
    
    public function marcarComoPagado(): void
    {
        if ($this->esFraccionado) {
            if (!$this->todosPlazosPagados()) {
                throw new DomainException('No se puede marcar como pagado hasta que todos los plazos estén pagados');
            }
        }
        
        $this->estado = EstadoPago::PAGADO;
        $this->fechaPagado = new DateTime();
        $this->recordEvent(new PagoProcesado($this->id, $this->servicioId));
    }
    
    private function todosPlazosPagados(): bool
    {
        return $this->plazos->every(fn(PagoPlazo $plazo) => $plazo->estaPagado());
    }
}

enum TipoPago: string
{
    case PRE_SERVICIO = 'pre_servicio';
    case EN_SERVICIO = 'en_servicio';
    case POST_SERVICIO = 'post_servicio';
}

enum ModoPago: string
{
    case BIZUM = 'bizum';
    case TRANSFERENCIA = 'transferencia';
    case EFECTIVO = 'efectivo';
}

enum EstadoPago: string
{
    case PENDIENTE = 'pendiente';
    case ORDENADO = 'ordenado';
    case PAGADO = 'pagado';
}
```

### 5.2 Entidad Pago Plazo

```php
class PagoPlazo implements Entity
{
    private PagoPlazoId $id;
    private PagoId $pagoId;
    private int $numeroPlazo;
    private Money $cantidad;
    private EstadoPago $estado;
    private ?DateTime $fechaPagado;
    private DateTime $fechaPrevista;
    
    public function marcarComoPagado(): void
    {
        $this->estado = EstadoPago::PAGADO;
        $this->fechaPagado = new DateTime();
    }
    
    public function estaPagado(): bool
    {
        return $this->estado === EstadoPago::PAGADO;
    }
}
```

### 5.3 Agregado Factura

```php
class Factura implements AggregateRoot
{
    private FacturaId $id;
    private ClienteId $clienteId;
    private DateTime $fecha;
    private Collection $servicios;
    private string $comentario;
    private Money $precio;
    private string $enlacePdf;
    private ?DatosExtraidos $datosIA;
    private bool $validada;
    
    public function procesarConIA(array $datosExtraidos): void
    {
        $this->datosIA = new DatosExtraidos($datosExtraidos);
        $this->recordEvent(new FacturaProcesadaConIA($this->id));
    }
    
    public function validarDatos(array $datosValidados): void
    {
        // Actualizar datos con validación manual
        $this->fecha = $datosValidados['fecha'] ?? $this->fecha;
        $this->precio = new Money($datosValidados['precio'] ?? $this->precio->getAmount());
        $this->comentario = $datosValidados['comentario'] ?? $this->comentario;
        $this->validada = true;
        
        $this->recordEvent(new FacturaValidada($this->id));
    }
    
    public function vincularServicios(array $serviciosIds): void
    {
        foreach ($serviciosIds as $servicioId) {
            $this->servicios->add(new ServicioId($servicioId));
        }
        
        $this->recordEvent(new ServiciosVinculadosAFactura($this->id, $serviciosIds));
    }
}

class DatosExtraidos
{
    private array $datos;
    private float $confianza;
    private DateTime $fechaExtraccion;
}
```

## 6. Value Objects Compartidos

```php
class Money
{
    private float $amount;
    private string $currency;
    
    public function __construct(float $amount, string $currency = 'EUR')
    {
        $this->amount = $amount;
        $this->currency = $currency;
    }
    
    public static function zero(): self
    {
        return new self(0.0);
    }
    
    public function add(Money $other): self
    {
        $this->ensureSameCurrency($other);
        return new self($this->amount + $other->amount, $this->currency);
    }
    
    public function subtract(Money $other): self
    {
        $this->ensureSameCurrency($other);
        return new self($this->amount - $other->amount, $this->currency);
    }
}

class Email
{
    private string $value;
    
    public function __construct(string $email)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Email inválido');
        }
        $this->value = $email;
    }
}

class Telefono
{
    private string $value;
    
    public function __construct(string $telefono)
    {
        $cleaned = preg_replace('/[^0-9+]/', '', $telefono);
        if (strlen($cleaned) < 9) {
            throw new InvalidArgumentException('Teléfono inválido');
        }
        $this->value = $cleaned;
    }
}
```

## 7. Domain Events

```php
interface DomainEvent
{
    public function getAggregateId(): string;
    public function getOccurredOn(): DateTime;
    public function getEventName(): string;
}

class ClienteConvertidoAContratado implements DomainEvent
{
    private ClienteId $clienteId;
    private DateTime $occurredOn;

    public function __construct(ClienteId $clienteId)
    {
        $this->clienteId = $clienteId;
        $this->occurredOn = new DateTime();
    }
}

class PresupuestoAceptado implements DomainEvent
{
    private PresupuestoId $presupuestoId;
    private ClienteId $clienteId;
    private DateTime $occurredOn;
}

class ServicioAcademicoCreado implements DomainEvent
{
    private ServicioId $servicioId;
    private ClienteId $clienteId;
    private DateTime $occurredOn;
}

class PagoProcesado implements DomainEvent
{
    private PagoId $pagoId;
    private ServicioId $servicioId;
    private DateTime $occurredOn;
}
```

## 8. Repositorios (Interfaces)

```php
interface ClienteRepository
{
    public function save(Cliente $cliente): void;
    public function findById(ClienteId $id): ?Cliente;
    public function findByNif(Nif $nif): ?Cliente;
    public function findAll(): Collection;
    public function findContratados(): Collection;
}

interface ExpedienteRepository
{
    public function save(Expediente $expediente): void;
    public function findById(ExpedienteId $id): ?Expediente;
    public function findByClienteId(ClienteId $clienteId): Collection;
}

interface ServicioRepository
{
    public function save(ServicioBase $servicio): void;
    public function findById(ServicioId $id): ?ServicioBase;
    public function findByClienteId(ClienteId $clienteId): Collection;
    public function findServiciosAcademicos(): Collection;
    public function findByFechas(DateTime $inicio, DateTime $fin): Collection;
}

interface PagoRepository
{
    public function save(Pago $pago): void;
    public function findById(PagoId $id): ?Pago;
    public function findByServicioId(ServicioId $servicioId): Collection;
    public function findPendientes(): Collection;
}

interface FacturaRepository
{
    public function save(Factura $factura): void;
    public function findById(FacturaId $id): ?Factura;
    public function findByClienteId(ClienteId $clienteId): Collection;
    public function findNoValidadas(): Collection;
}
```
