# Especificaciones Técnicas - CRM Personalizado

## 1. Stack Tecnológico

### 1.1 Backend
- **Lenguaje**: PHP 8.3
- **Framework**: Symfony 6.4 LTS
- **Base de Datos**: MariaDB 10.11
- **ORM**: Doctrine ORM 3.0
- **Validación**: Symfony Validator
- **Serialización**: Symfony Serializer
- **Testing**: PHPUnit 10.x
- **Aná<PERSON>is de Código**: PHPStan Level 8, PHP-CS-Fixer

### 1.2 Frontend
- **Framework**: React 18.x
- **Lenguaje**: TypeScript 5.x
- **Build Tool**: Vite
- **UI Library**: Material-UI (MUI) 5.x
- **Estado**: React Query + Zustand
- **Routing**: React Router 6.x
- **Forms**: React Hook Form + Zod
- **Testing**: Jest + React Testing Library

### 1.3 Infraestructura
- **Contenedores**: <PERSON>er + Docker Compose
- **Servidor Web**: Nginx
- **PHP-FPM**: 8.3
- **Cache**: Redis
- **Queue**: Symfony Messenger + Redis Transport
- **File Storage**: Local Storage + AWS S3 (opcional)
- **Email**: Symfony Mailer + SMTP
- **IA**: OpenAI API / Azure OpenAI

## 2. Configuración del Entorno

### 2.1 Docker Compose

```yaml
version: '3.8'

services:
  apache:
    image: php:8.3-apache
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/var/www/html
      - ./docker/apache/000-default.conf:/etc/apache2/sites-available/000-default.conf
      - ./docker/apache/apache2.conf:/etc/apache2/apache2.conf
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    depends_on:
      - mariadb
      - redis



  mariadb:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: crm_db
      MYSQL_USER: crm_user
      MYSQL_PASSWORD: crm_pass
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./docker/mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  node:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./frontend:/app
    command: npm run dev
    ports:
      - "5173:5173"

volumes:
  mariadb_data:
  redis_data:
```

### 2.2 Configuración Apache

#### **Virtual Host (docker/apache/000-default.conf)**
```apache
<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/html/public

    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted

        # Symfony routing
        FallbackResource /index.php
    </Directory>

    # Logs
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    # PHP configuration
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value memory_limit 256M
</VirtualHost>

# SSL Virtual Host (opcional)
<VirtualHost *:443>
    ServerName localhost
    DocumentRoot /var/www/html/public

    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/localhost.crt
    SSLCertificateKeyFile /etc/ssl/private/localhost.key

    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        FallbackResource /index.php
    </Directory>
</VirtualHost>
```

#### **Configuración Principal Apache (docker/apache/apache2.conf)**
```apache
# Global configuration
ServerRoot /etc/apache2
PidFile ${APACHE_PID_FILE}
Timeout 300
KeepAlive On
MaxKeepAliveRequests 100
KeepAliveTimeout 5

# Modules
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule ssl_module modules/mod_ssl.so
LoadModule headers_module modules/mod_headers.so

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Include other configs
IncludeOptional conf-enabled/*.conf
IncludeOptional sites-enabled/*.conf
```

#### **Dockerfile Apache + PHP**
```dockerfile
FROM php:8.3-apache

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    libicu-dev \
    && docker-php-ext-configure intl \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip intl

# Habilitar módulos Apache
RUN a2enmod rewrite ssl headers deflate

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar directorio de trabajo
WORKDIR /var/www/html

# Configurar Apache
ENV APACHE_DOCUMENT_ROOT=/var/www/html/public
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

# Copiar archivos de configuración
COPY composer.json composer.lock ./
RUN composer install --no-scripts --no-autoloader

# Copiar código fuente
COPY . .
RUN composer dump-autoload --optimize

# Configurar permisos
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

EXPOSE 80 443
```

## 3. Configuración de Base de Datos

### 3.1 Schema Principal

```sql
-- Tabla Clientes
CREATE TABLE clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_manual VARCHAR(50) UNIQUE NOT NULL,
    nif VARCHAR(15) UNIQUE NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    apellidos VARCHAR(255) NOT NULL,
    telefono1 VARCHAR(20) NOT NULL,
    telefono2 VARCHAR(20),
    telefono3 VARCHAR(20),
    email1 VARCHAR(255) NOT NULL,
    email2 VARCHAR(255),
    email3 VARCHAR(255),
    tipo ENUM('potencial', 'contratado') DEFAULT 'potencial',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_nif (nif),
    INDEX idx_tipo (tipo),
    INDEX idx_nombre (nombre, apellidos)
);

-- Tabla Expedientes
CREATE TABLE expedientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cliente_id INT NOT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resumen TEXT NOT NULL,
    texto LONGTEXT,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
    INDEX idx_cliente (cliente_id),
    INDEX idx_fecha (fecha_creacion)
);

-- Tabla Comunicaciones
CREATE TABLE comunicaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cliente_id INT NOT NULL,
    expediente_id INT,
    servicio_id INT,
    fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    medio ENUM('email', 'telefono', 'whatsapp') NOT NULL,
    texto LONGTEXT NOT NULL,
    resumen TEXT NOT NULL,
    es_servicio_academico BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
    FOREIGN KEY (expediente_id) REFERENCES expedientes(id) ON DELETE SET NULL,
    INDEX idx_cliente_fecha (cliente_id, fecha_hora),
    INDEX idx_medio (medio),
    INDEX idx_academico (es_servicio_academico)
);

-- Tabla Presupuestos
CREATE TABLE presupuestos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    expediente_id INT NOT NULL,
    cliente_id INT NOT NULL,
    fecha TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tipos_servicios JSON NOT NULL,
    descripcion_concepto TEXT,
    precio DECIMAL(10,2),
    enlace_pdf VARCHAR(500) NOT NULL,
    estado ENUM('generado', 'enviado', 'aceptado', 'rechazado') DEFAULT 'generado',
    FOREIGN KEY (expediente_id) REFERENCES expedientes(id) ON DELETE CASCADE,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
    INDEX idx_estado (estado),
    INDEX idx_fecha (fecha)
);

-- Tabla Servicios
CREATE TABLE servicios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cliente_id INT NOT NULL,
    tipo_servicio ENUM(
        'acreditacion_titular_universitario',
        'acreditacion_catedratico_universidad', 
        'acreditacion_contratado_doctor',
        'sexenio',
        'gestion_curriculum',
        'creacion_pagina_web',
        'actualizacion_pagina_web',
        'gestion_informacion',
        'academico'
    ) NOT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comentario TEXT,
    discriminator VARCHAR(50) NOT NULL,
    -- Campos para Gestión Servicio
    fecha_inicio TIMESTAMP NULL,
    fecha_prevista_finalizacion TIMESTAMP NULL,
    precio DECIMAL(10,2) NULL,
    pagado DECIMAL(10,2) DEFAULT 0.00,
    pendiente_pago DECIMAL(10,2) NULL,
    -- Campos para Servicio Académico
    texto_comunicacion LONGTEXT NULL,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
    INDEX idx_cliente_tipo (cliente_id, tipo_servicio),
    INDEX idx_discriminator (discriminator),
    INDEX idx_fechas (fecha_inicio, fecha_prevista_finalizacion)
);

-- Tabla Pagos
CREATE TABLE pagos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    servicio_id INT NOT NULL,
    tipo ENUM('pre_servicio', 'en_servicio', 'post_servicio') NOT NULL,
    modo_pago ENUM('bizum', 'transferencia', 'efectivo') NOT NULL,
    comentario TEXT,
    cantidad DECIMAL(10,2) NOT NULL,
    estado ENUM('pendiente', 'ordenado', 'pagado') DEFAULT 'pendiente',
    fecha_pagado TIMESTAMP NULL,
    es_fraccionado BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (servicio_id) REFERENCES servicios(id) ON DELETE CASCADE,
    INDEX idx_servicio (servicio_id),
    INDEX idx_estado (estado),
    INDEX idx_tipo (tipo)
);

-- Tabla Plazos de Pago
CREATE TABLE pagos_plazos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pago_id INT NOT NULL,
    numero_plazo INT NOT NULL,
    cantidad DECIMAL(10,2) NOT NULL,
    fecha_prevista TIMESTAMP NOT NULL,
    estado ENUM('pendiente', 'ordenado', 'pagado') DEFAULT 'pendiente',
    fecha_pagado TIMESTAMP NULL,
    FOREIGN KEY (pago_id) REFERENCES pagos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_pago_plazo (pago_id, numero_plazo),
    INDEX idx_estado (estado),
    INDEX idx_fecha_prevista (fecha_prevista)
);

-- Tabla Facturas
CREATE TABLE facturas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cliente_id INT NOT NULL,
    fecha TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comentario TEXT,
    precio DECIMAL(10,2),
    enlace_pdf VARCHAR(500) NOT NULL,
    datos_ia JSON,
    validada BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
    INDEX idx_cliente (cliente_id),
    INDEX idx_fecha (fecha),
    INDEX idx_validada (validada)
);

-- Tabla Relación Facturas-Servicios
CREATE TABLE factura_servicios (
    factura_id INT NOT NULL,
    servicio_id INT NOT NULL,
    PRIMARY KEY (factura_id, servicio_id),
    FOREIGN KEY (factura_id) REFERENCES facturas(id) ON DELETE CASCADE,
    FOREIGN KEY (servicio_id) REFERENCES servicios(id) ON DELETE CASCADE
);
```

### 3.2 Índices de Rendimiento

```sql
-- Índices compuestos para consultas frecuentes
CREATE INDEX idx_cliente_servicio_fecha ON servicios(cliente_id, fecha_creacion);
CREATE INDEX idx_pago_servicio_estado ON pagos(servicio_id, estado);
CREATE INDEX idx_comunicacion_cliente_fecha ON comunicaciones(cliente_id, fecha_hora DESC);

-- Índices para búsquedas de texto
CREATE FULLTEXT INDEX idx_cliente_nombre_fulltext ON clientes(nombre, apellidos);
CREATE FULLTEXT INDEX idx_comunicacion_texto_fulltext ON comunicaciones(texto, resumen);
```

## 4. Configuración de Symfony

### 4.1 Configuración Principal

```yaml
# config/packages/framework.yaml
framework:
    secret: '%env(APP_SECRET)%'
    http_method_override: false
    handle_all_throwables: true
    php_errors:
        log: true
    session:
        handler_id: 'session.handler.redis'
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native

# config/packages/doctrine.yaml
doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
        charset: utf8mb4
        default_table_options:
            charset: utf8mb4
            collate: utf8mb4_unicode_ci
        types:
            cliente_id: App\Infrastructure\Persistence\Doctrine\Type\ClienteIdType
            servicio_id: App\Infrastructure\Persistence\Doctrine\Type\ServicioIdType
            pago_id: App\Infrastructure\Persistence\Doctrine\Type\PagoIdType
    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            App:
                is_bundle: false
                dir: '%kernel.project_dir%/src/Domain'
                prefix: 'App\Domain'
                type: attribute

# config/packages/messenger.yaml
framework:
    messenger:
        failure_transport: failed
        transports:
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                options:
                    use_notify: true
                    check_delayed_interval: 60000
                retry_strategy:
                    max_retries: 3
                    multiplier: 2
            failed: 'doctrine://default?queue_name=failed'
        routing:
            App\Domain\*\Event\*: async
```

### 4.2 Configuración de Servicios

```yaml
# config/services.yaml
parameters:
    upload_directory: '%kernel.project_dir%/var/uploads'
    openai_api_key: '%env(OPENAI_API_KEY)%'

services:
    _defaults:
        autowire: true
        autoconfigure: true
        bind:
            $uploadDirectory: '%upload_directory%'
            $openaiApiKey: '%openai_api_key%'

    # Repositories
    App\Domain\Cliente\Repository\ClienteRepositoryInterface:
        class: App\Infrastructure\Persistence\Doctrine\DoctrineClienteRepository

    App\Domain\Servicio\Repository\ServicioRepositoryInterface:
        class: App\Infrastructure\Persistence\Doctrine\DoctrineServicioRepository

    # External Services
    App\Infrastructure\External\Email\EmailServiceInterface:
        class: App\Infrastructure\External\Email\SymfonyMailerEmailService

    App\Infrastructure\External\IA\IAServiceInterface:
        class: App\Infrastructure\External\IA\OpenAIService

    # Command Bus
    App\Application\Command\CommandBusInterface:
        class: App\Infrastructure\Bus\SymfonyCommandBus

    # Query Bus  
    App\Application\Query\QueryBusInterface:
        class: App\Infrastructure\Bus\SymfonyQueryBus

    # Event Dispatcher
    App\Domain\Shared\Event\DomainEventDispatcherInterface:
        class: App\Infrastructure\Event\SymfonyDomainEventDispatcher
```

## 5. Configuración Frontend

### 5.1 Package.json

```json
{
  "name": "crm-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@mui/material": "^5.14.0",
    "@mui/icons-material": "^5.14.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "react-router-dom": "^6.15.0",
    "react-hook-form": "^7.45.0",
    "zod": "^3.22.0",
    "@hookform/resolvers": "^3.3.0",
    "@tanstack/react-query": "^4.32.0",
    "zustand": "^4.4.0",
    "axios": "^1.5.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0",
    "jest": "^29.6.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.17.0"
  }
}
```

### 5.2 Vite Configuration

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@types': path.resolve(__dirname, './src/types'),
      '@utils': path.resolve(__dirname, './src/utils'),
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:80',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: '../public/build',
    emptyOutDir: true,
  },
})
```

## 6. Configuración de Calidad de Código

### 6.1 PHPStan Configuration

```neon
# phpstan.neon
parameters:
    level: 8
    paths:
        - src
    excludePaths:
        - src/Kernel.php
    ignoreErrors:
        - '#Call to an undefined method Doctrine\\Persistence\\ObjectRepository::#'
    doctrine:
        objectManagerLoader: tests/object-manager.php
```

### 6.2 PHP-CS-Fixer Configuration

```php
// .php-cs-fixer.php
<?php

$finder = PhpCsFixer\Finder::create()
    ->in(__DIR__ . '/src')
    ->in(__DIR__ . '/tests');

return (new PhpCsFixer\Config())
    ->setRules([
        '@PSR12' => true,
        '@Symfony' => true,
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => true,
        'no_unused_imports' => true,
        'declare_strict_types' => true,
    ])
    ->setFinder($finder);
```

## 7. Scripts de Deployment

### 7.1 Makefile

```makefile
.PHONY: install start stop test lint

install:
	docker-compose build
	docker-compose run --rm php composer install
	docker-compose run --rm node npm install
	docker-compose run --rm php bin/console doctrine:database:create --if-not-exists
	docker-compose run --rm php bin/console doctrine:migrations:migrate --no-interaction

start:
	docker-compose up -d

stop:
	docker-compose down

test:
	docker-compose run --rm php vendor/bin/phpunit
	docker-compose run --rm node npm test

lint:
	docker-compose run --rm php vendor/bin/phpstan analyse
	docker-compose run --rm php vendor/bin/php-cs-fixer fix --dry-run
	docker-compose run --rm node npm run lint

fix:
	docker-compose run --rm php vendor/bin/php-cs-fixer fix

migrate:
	docker-compose run --rm php bin/console doctrine:migrations:migrate --no-interaction

cache-clear:
	docker-compose run --rm php bin/console cache:clear
```
